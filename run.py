import os
import argparse  # Pour les arguments de ligne de commande
from app import create_app, db
from config import Config
from app.modules.auth.models import User, UserRole

app = create_app(Config)

# Variable globale pour suivre si les données de démonstration ont déjà été chargées
_demo_data_loaded = False

@app.shell_context_processor
def make_shell_context():
    return {'db': db, 'User': User, 'UserRole': UserRole}

def init_db(force_recreate=False):
    with app.app_context():
        # Si on force la recréation, on supprime toutes les tables
        if force_recreate:
            print("Suppression de toutes les tables de la base de données...")
            db.drop_all()
            print("Tables supprimées.")
        
        # Création des tables
        db.create_all()
        
        # Vérifier si un utilisateur system_admin existe déjà
        existing_admin = User.query.filter_by(role=UserRole.SYSTEM_ADMIN).first()
        
        
        if not existing_admin:
            # Créer un utilisateur system_admin par défaut
            system_admin = User(
                username='admin',
                email='<EMAIL>',
                role=UserRole.SYSTEM_ADMIN,
                created_by_id=None  # Le SYSTEM_ADMIN initial n'a pas de créateur
            )
            system_admin.set_password('admin123')  # Mot de passe temporaire
            db.session.add(system_admin)
            db.session.commit()
            print('Utilisateur system_admin créé avec succès!')
            print('Email: <EMAIL>')
            print('Mot de passe: admin123')
        
        
        print('Base de données initialisée avec succès!')

def load_demo_data():
    """Charge les données de démonstration si le dossier demo_data existe"""
    global _demo_data_loaded
    
    # Si les données ont déjà été chargées dans cette exécution, ne pas les recharger
    if _demo_data_loaded:
        print("Les données de démonstration ont déjà été chargées.")
        return
    
    try:
        import demo_data.generate_demo_data
        print("Chargement des données de démonstration...")
        demo_data.generate_demo_data.main()
        _demo_data_loaded = True
    except ImportError:
        print("Aucune donnée de démonstration à charger.")
    except Exception as e:
        print(f"Erreur lors du chargement des données de démonstration: {str(e)}")
        # Affichage détaillé de l'erreur pour faciliter le débogage
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    # Configurer l'analyseur d'arguments
    parser = argparse.ArgumentParser(description='Lancer l\'application POS')
    parser.add_argument('--demo', action='store_true', help='Charger des données de démonstration')
    args = parser.parse_args()
    
    # En mode démo, forcer la recréation de la base de données
    force_recreate = args.demo
    init_db(force_recreate)  # Initialiser la base de données au démarrage
    
    # Charger les données de démonstration si demandé et si le module existe
    if args.demo and os.path.exists('demo_data'):
        load_demo_data()
        
    app.run(debug=True) 