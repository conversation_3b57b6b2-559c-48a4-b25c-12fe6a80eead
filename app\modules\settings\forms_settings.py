from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import <PERSON>Field, TextAreaField, FloatField, BooleanField, DecimalField, SelectField, TimeField, SubmitField, IntegerField, SelectMultipleField
from wtforms.validators import DataRequired, Length, Optional, NumberRange, Email, URL

class BusinessSettingsForm(FlaskForm):
    business_name = StringField('Nom de l\'entreprise', validators=[
        DataRequired(),
        Length(min=2, max=100)
    ])
    business_legal_name = StringField('Raison sociale', validators=[
        DataRequired(),
        Length(min=2, max=100)
    ])
    business_type = SelectField('Type d\'entreprise', choices=[
        ('restaurant', 'Restaurant'),
        ('retail', 'Commerce de détail'),
        ('bakery', 'Boulangerie'),
        ('cafe', 'Café'),
        ('other', 'Autre')
    ])
    tax_id = StringField('Numéro de TVA', validators=[Optional(), Length(max=50)])
    address = TextAreaField('Adresse', validators=[Optional(), Length(max=200)])
    postal_code = StringField('Code postal', validators=[Optional(), Length(max=10)])
    city = StringField('Ville', validators=[Optional(), Length(max=100)])
    country = SelectField('Pays', choices=[
        ('FR', 'France'),
        ('BE', 'Belgique'),
        ('CH', 'Suisse'),
        ('CA', 'Canada'),
        ('Other', 'Autre')
    ], validators=[Optional()])
    phone = StringField('Téléphone')
    email = StringField('Email', validators=[Optional(), Email()])
    website = StringField('Site web', validators=[Optional(), URL()])
    logo = FileField('Logo', validators=[
        FileAllowed(['jpg', 'png', 'jpeg'], 'Images uniquement!')
    ])
    currency = SelectField('Devise', choices=[
        ('EUR', 'Euro (€)'),
        ('USD', 'Dollar ($)'),
        ('GBP', 'Livre sterling (£)')
    ])
    timezone = SelectField('Fuseau horaire')
    language = SelectField('Langue', choices=[
        ('fr', 'Français'),
        ('en', 'English')
    ])
    date_format = SelectField('Format de date', choices=[
        ('%d/%m/%Y', 'JJ/MM/AAAA'),
        ('%Y-%m-%d', 'AAAA-MM-JJ'),
        ('%d-%m-%Y', 'JJ-MM-AAAA'),
        ('%d.%m.%Y', 'JJ.MM.AAAA')
    ])
    time_format = SelectField('Format d\'heure', choices=[
        ('%H:%M', '24 heures (14:30)'),
        ('%I:%M %p', '12 heures (02:30 PM)')
    ])
    first_day_of_week = SelectField('Premier jour de la semaine', choices=[
        ('0', 'Dimanche'),
        ('1', 'Lundi')
    ])
    default_tax_rate = FloatField('Taux de TVA par défaut', validators=[
        Optional(),
        NumberRange(min=0, max=100)
    ])
    low_stock_threshold = IntegerField('Seuil de stock bas', validators=[
        Optional(),
        NumberRange(min=0)
    ])
    session_timeout = IntegerField('Délai d\'expiration de session', validators=[
        Optional(),
        NumberRange(min=1, max=1440)  # 1 minute à 24 heures
    ])
    backup_enabled = BooleanField('Sauvegardes automatiques')
    backup_frequency = SelectField('Fréquence des sauvegardes', choices=[
        ('daily', 'Quotidienne'),
        ('weekly', 'Hebdomadaire'),
        ('monthly', 'Mensuelle')
    ], validators=[Optional()])
    backup_retention = IntegerField('Durée de conservation (jours)', validators=[
        Optional(),
        NumberRange(min=1, max=365)  # 1 jour à 1 an
    ])
    submit = SubmitField('Enregistrer')

class ReceiptSettingsForm(FlaskForm):
    header_text = TextAreaField('En-tête')
    footer_text = TextAreaField('Pied de page')
    show_logo = BooleanField('Afficher le logo')
    show_tax_details = BooleanField('Détails des taxes')
    show_payment_details = BooleanField('Détails du paiement')
    show_server_name = BooleanField('Nom du serveur')
    show_order_number = BooleanField('Numéro de commande')
    show_table_number = BooleanField('Numéro de table')
    
    # Layout settings
    paper_size = SelectField('Format du papier', choices=[
        ('80mm', '80mm (standard)'),
        ('58mm', '58mm (compact)'),
        ('custom', 'Personnalisé')
    ])
    paper_width = FloatField('Largeur du papier', validators=[Optional(), NumberRange(min=20, max=200)])
    font_family = SelectField('Police', choices=[
        ('monospace', 'Monospace'),
        ('sans-serif', 'Sans-serif')
    ])
    font_size = FloatField('Taille de police', validators=[Optional(), NumberRange(min=6, max=24)])
    line_spacing = FloatField('Interligne', validators=[Optional(), NumberRange(min=1, max=3)])
    margin_top = FloatField('Marge supérieure', validators=[Optional(), NumberRange(min=0, max=50)])
    margin_bottom = FloatField('Marge inférieure', validators=[Optional(), NumberRange(min=0, max=50)])
    margin_left = FloatField('Marge gauche', validators=[Optional(), NumberRange(min=0, max=50)])
    margin_right = FloatField('Marge droite', validators=[Optional(), NumberRange(min=0, max=50)])
    
    # Printer settings
    printer_name = SelectField('Imprimante')
    copies = IntegerField('Nombre de copies', validators=[Optional(), NumberRange(min=1, max=10)])
    auto_print = BooleanField('Impression automatique')
    cut_paper = BooleanField('Couper le papier')
    open_cash_drawer = BooleanField('Ouvrir le tiroir-caisse')
    
    submit = SubmitField('Enregistrer')

class SaleSettingsForm(FlaskForm):
    default_tax_rate = FloatField('Taux de TVA par défaut (%)', validators=[
        Optional(),
        NumberRange(min=0, max=100)
    ])
    allow_decimal_quantity = BooleanField('Autoriser les quantités décimales')
    allow_price_change = BooleanField('Autoriser la modification des prix')
    allow_discount = BooleanField('Autoriser les remises')
    require_customer_info = BooleanField('Informations client obligatoires')
    require_table_selection = BooleanField('Sélection de table obligatoire')
    enable_kitchen_print = BooleanField('Activer l\'impression cuisine')
    enable_low_stock_alert = BooleanField('Alertes stock bas')
    submit = SubmitField('Enregistrer')

class CashRegisterSettingsForm(FlaskForm):
    name = StringField('Nom de la caisse', validators=[DataRequired()])
    number = StringField('Numéro de la caisse', validators=[DataRequired()])
    require_float = BooleanField('Exiger un montant initial')
    default_float = DecimalField('Montant initial par défaut')
    require_close = BooleanField('Exiger la fermeture')
    allow_delete = BooleanField('Autoriser la suppression')
    allow_void = BooleanField('Autoriser l\'annulation')
    require_reason = BooleanField('Exiger une raison')

    # à verifier avec sa fontion et template   
    require_opening_amount = BooleanField('Montant d\'ouverture obligatoire')
    require_closing_amount = BooleanField('Montant de fermeture obligatoire')
    allow_negative_balance = BooleanField('Autoriser le solde négatif')
    max_difference = FloatField('Différence maximale autorisée', validators=[Optional()])
    submit = SubmitField('Enregistrer')

class SystemSettingsForm(FlaskForm):
    session_lifetime = FloatField('Durée de session (minutes)', validators=[
        DataRequired(),
        NumberRange(min=1)
    ])
    max_login_attempts = FloatField('Tentatives de connexion max', validators=[
        DataRequired(),
        NumberRange(min=1)
    ])
    password_expiry_days = FloatField('Expiration mot de passe (jours)', validators=[Optional()])
    enable_2fa = BooleanField('Activer 2FA')
    submit = SubmitField('Enregistrer')

class AlertSettingsForm(FlaskForm):
    enable_email_alerts = BooleanField('Alertes par email')
    alert_email = StringField('Email pour les alertes', validators=[Optional(), Email()])
    low_stock_threshold = FloatField('Seuil stock bas', validators=[Optional()])
    high_amount_threshold = FloatField('Seuil montant élevé', validators=[Optional()])
    submit = SubmitField('Enregistrer')

class MaintenanceSettingsForm(FlaskForm):
    # Maintenance Settings
    maintenance_mode = BooleanField('Mode maintenance')
    maintenance_message = TextAreaField('Message de maintenance', validators=[Optional()])
    allowed_ips = TextAreaField('IPs autorisées', validators=[Optional()], description='Une adresse IP par ligne')

    # Cleanup Settings
    cleanup_enabled = BooleanField('Activer le nettoyage automatique')
    cleanup_frequency = SelectField('Fréquence de nettoyage', choices=[
        ('daily', 'Quotidien'),
        ('weekly', 'Hebdomadaire'),
        ('monthly', 'Mensuel')
    ])
    cleanup_age = IntegerField('Âge des données à nettoyer (jours)', validators=[
        Optional(),
        NumberRange(min=1, max=365)
    ])
    optimize_tables = BooleanField('Optimiser les tables')
    optimize_frequency = SelectField('Fréquence d\'optimisation', choices=[
        ('daily', 'Quotidienne'),
        ('weekly', 'Hebdomadaire'),
        ('monthly', 'Mensuelle')
    ])

    # Backup Settings
    backup_enabled = BooleanField('Sauvegardes automatiques')
    backup_frequency = SelectField('Fréquence', choices=[
        ('daily', 'Quotidienne'),
        ('weekly', 'Hebdomadaire'),
        ('monthly', 'Mensuelle')
    ])
    backup_time = TimeField('Heure de sauvegarde')
    backup_retention = FloatField('Durée de conservation', validators=[
        Optional(),
        NumberRange(min=1, max=365)
    ])
    backup_location = StringField('Emplacement des sauvegardes', validators=[
        Optional(),
        Length(max=255)
    ])
    backup_compress = BooleanField('Compression')
    submit = SubmitField('Enregistrer')

class CashRegisterForm(FlaskForm):
    opening_amount = FloatField('Montant d\'ouverture', validators=[
        DataRequired(),
        NumberRange(min=0)
    ])
    note = TextAreaField('Note')
    submit = SubmitField('Ouvrir la caisse')

class CashRegisterCloseForm(FlaskForm):
    closing_amount = FloatField('Montant de fermeture', validators=[
        DataRequired(),
        NumberRange(min=0)
    ])
    note = TextAreaField('Note')
    submit = SubmitField('Fermer la caisse')

class PaymentSettingsForm(FlaskForm):
    # Payment Methods
    accepted_payment_methods = SelectMultipleField('Moyens de paiement acceptés', choices=[
        ('cash', 'Espèces'),
        ('card', 'Carte bancaire'),
        ('check', 'Chèque'),
        ('transfer', 'Virement'),
        ('mobile_payment', 'Paiement mobile')
    ], validators=[DataRequired()])
    
    default_payment_method = SelectField('Mode de paiement par défaut', choices=[
        ('cash', 'Espèces'),
        ('card', 'Carte bancaire'),
        ('check', 'Chèque'),
        ('transfer', 'Virement'),
        ('mobile_payment', 'Paiement mobile')
    ], validators=[DataRequired()])
    
    allow_split_payment = BooleanField('Paiement fractionné')
    allow_partial_payment = BooleanField('Paiement partiel')
    require_payment_reference = BooleanField('Référence de paiement')
    round_amounts = SelectField('Arrondi des montants', choices=[
        ('none', 'Aucun'),
        ('0.05', '0.05'),
        ('0.10', '0.10'),
        ('0.50', '0.50'),
        ('1.00', '1.00')
    ])

    # Refund Settings
    allow_refund = BooleanField('Autoriser les remboursements')
    refund_limit = FloatField('Limite de remboursement', validators=[Optional(), NumberRange(min=0)])
    require_approval = BooleanField('Approbation requise')
    approval_limit = FloatField('Limite d\'approbation', validators=[Optional(), NumberRange(min=0)])

    # Card Payment Settings
    card_terminal = SelectField('Terminal de paiement', choices=[
        ('none', 'Aucun'),
        ('sumup', 'SumUp'),
        ('izettle', 'iZettle'),
        ('terminal', 'Terminal classique'),
        ('other', 'Autre')
    ])
    card_minimum = FloatField('Montant minimum', validators=[Optional(), NumberRange(min=0)])
    card_fee = FloatField('Frais de carte (%)', validators=[Optional(), NumberRange(min=0, max=100)])

    # Tax Settings
    tax_number = StringField('Numéro de TVA', validators=[Optional(), Length(max=50)])
    tax_enabled = BooleanField('Gestion des taxes')
    prices_include_tax = BooleanField('Prix TTC')
    default_tax_rate = FloatField('Taux de TVA par défaut (%)', validators=[Optional(), NumberRange(min=0, max=100)])

    submit = SubmitField('Enregistrer') 