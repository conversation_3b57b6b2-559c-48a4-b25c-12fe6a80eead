{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
            <i class="fas fa-shopping-cart"></i> {{ title }}
        </h2>
        <a href="{{ url_for('pos.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour au POS
        </a>
    </div>

    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET">
                <!-- Première ligne de filtres -->
                <div class="row g-3 mb-3">
                    <div class="col-md-2">
                        <label class="form-label">Statut</label>
                        <select name="status" class="form-select">
                            <option value="">Tous les statuts</option>
                            {% for status_value, status_label in statuses %}
                            <option value="{{ status_value }}" {% if status == status_value %}selected{% endif %}>
                                {% if status_value == 'pending' %}En attente
                                {% elif status_value == 'kitchen_pending' %}Cuisine en attente
                                {% elif status_value == 'kitchen_ready' %}Cuisine prêt
                                {% elif status_value == 'delivered' %}Servi
                                {% elif status_value == 'paid' %}Payé
                                {% elif status_value == 'cancelled' %}Annulé
                                {% elif status_value == 'completed' %}Terminé
                                {% elif status_value == 'voided' %}Annulé (void)
                                {% elif status_value == 'ready' %}Prêt
                                {% else %}{{ status_value|title }}
                                {% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Utilisateur</label>
                        <select name="user_id" class="form-select">
                            <option value="">Tous</option>
                            {% for user in users %}
                            <option value="{{ user.id }}" {% if user_id and user.id == user_id|int %}selected{% endif %}>
                                {{ user.username }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Salle</label>
                        <select name="room_id" class="form-select">
                            <option value="">Toutes</option>
                            {% for room in rooms %}
                            <option value="{{ room.id }}" {% if room_id and room.id == room_id|int %}selected{% endif %}>
                                {{ room.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Type de service</label>
                        <select name="service_type" class="form-select">
                            <option value="">Tous les types</option>
                            {% for service_value, service_label in service_types %}
                            <option value="{{ service_value }}" {% if service_type == service_value %}selected{% endif %}>
                                {{ service_label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <!-- Deuxième ligne de filtres -->
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Date début</label>
                        <input type="date" name="date_from" class="form-control" value="{{ date_from.strftime('%Y-%m-%d') if date_from }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Date fin</label>
                        <input type="date" name="date_to" class="form-control" value="{{ date_to.strftime('%Y-%m-%d') if date_to }}">
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i> Filtrer
                        </button>
                        <a href="{{ url_for('pos.sales') }}" class="btn btn-secondary ms-2">
                            <i class="fas fa-times"></i> Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des ventes -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Référence</th>
                            <th>Date</th>
                            <th>Utilisateur</th>
                            <th>Client</th>
                            <th>Table/Salle</th>
                            <th>Type de service</th>
                            <th>Total</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for sale in sales.items %}
                        <tr>
                            <td>{{ sale.reference }}</td>
                            <td>{{ sale.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                            <td>
                                {% if sale.user %}
                                    <span class="badge bg-info">{{ sale.user.username }}</span>
                                {% else %}
                                    <span class="text-muted">Non défini</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if sale.customer %}
                                {{ sale.customer.first_name }} {{ sale.customer.last_name }}
                                {% else %}
                                Client anonyme
                                {% endif %}
                            </td>
                            <td>
                                {% if sale.table %}
                                    <strong>Table {{ sale.table.number }}</strong>
                                    {% if sale.table.room %}
                                        <br><small class="text-muted">{{ sale.table.room.name }}</small>
                                    {% elif sale.table.location %}
                                        <br><small class="text-muted">({{ sale.table.location }})</small>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">Non attribuée</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if sale.service_type %}
                                    {% if sale.service_type == 'dine_in' %}
                                        <span class="badge bg-primary">Sur place</span>
                                    {% elif sale.service_type == 'takeaway' %}
                                        <span class="badge bg-warning">À emporter</span>
                                    {% elif sale.service_type == 'delivery' %}
                                        <span class="badge bg-info">Livraison</span>
                                    {% elif sale.service_type == 'drive_thru' %}
                                        <span class="badge bg-success">Service au volant</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ sale.service_type }}</span>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">Non défini</span>
                                {% endif %}
                            </td>
                            <td>{{ "%.2f"|format(sale.total) }} €</td>
                            <td>
                                {% if sale.status.value == 'pending' %}
                                <span class="badge bg-warning">En attente</span>
                                {% elif sale.status.value == 'kitchen_pending' %}
                                <span class="badge bg-primary">Cuisine en attente</span>
                                {% elif sale.status.value == 'kitchen_ready' %}
                                <span class="badge bg-info">Cuisine prêt</span>
                                {% elif sale.status.value == 'delivered' %}
                                <span class="badge bg-secondary">Servi</span>
                                {% elif sale.status.value == 'paid' %}
                                <span class="badge bg-success">Payé</span>
                                {% elif sale.status.value == 'cancelled' %}
                                <span class="badge bg-danger">Annulé</span>
                                {% elif sale.status.value == 'completed' %}
                                <span class="badge bg-dark">Terminé</span>
                                {% else %}
                                <span class="badge bg-light text-dark">{{ sale.status.value }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('pos.sale_details', id=sale.id) }}"
                                   class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="9" class="text-center">Aucune vente trouvée</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if sales.pages > 1 %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% for page in range(1, sales.pages + 1) %}
                    <li class="page-item {% if page == sales.page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('pos.sales',
                            page=page,
                            status=status,
                            user_id=user_id,
                            room_id=room_id,
                            service_type=service_type,
                            date_from=date_from.strftime('%Y-%m-%d') if date_from,
                            date_to=date_to.strftime('%Y-%m-%d') if date_to) }}">
                            {{ page }}
                        </a>
                    </li>
                    {% endfor %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}