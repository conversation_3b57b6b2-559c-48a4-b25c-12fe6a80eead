<div class="cart-container">
    <!-- Add the posSettings element with required configuration -->
    <div id="posSettings" data-require-table="false" class="d-none"></div>

    <!-- Sélection de table -->
    <div class="table-selection mb-3">
        <label for="tableSelect" class="form-label">
            <i class="fas fa-table"></i> Sélectionner une table
        </label>
        <select class="form-select" id="tableSelect">
            <option value="">Aucune table</option>
            {% for table in tables %}
            <option value="{{ table.id }}"
                    data-number="{{ table.number }}"
                    data-location="{{ table.location or '' }}"
                    data-capacity="{{ table.capacity }}"
                    data-status="{{ table.status }}"
                    {% if table.status != 'available' %}disabled{% endif %}>
                Table {{ table.number }}
                {% if table.location %}({{ table.location }}){% endif %}
                - {{ table.capacity }} pers.
                {% if table.status != 'available' %}
                    - {% if table.status == 'occupied' %}Occupée{% elif table.status == 'reserved' %}Réservée{% elif table.status == 'cleaning' %}Nettoyage{% endif %}
                {% endif %}
            </option>
            {% endfor %}
        </select>
        <div class="selected-table-info mt-2">
            <small id="selectedTableInfo" class="text-muted"></small>
        </div>
    </div>

    <!-- En-tête du panier avec bouton pour voir le détail -->
    <div class="cart-header">
        <div class="cart-summary" onclick="showCartModal()">
            <div class="cart-info">
                <span class="cart-items-count">
                    <i class="fas fa-shopping-cart"></i>
                    <span id="cartItemsCount">0</span> article(s)
                </span>
                <span class="cart-total-preview">
                    <span id="cartTotalPreview">0.00 €</span>
                </span>
            </div>
            <i class="fas fa-chevron-right"></i>
        </div>
    </div>

    <!-- Zone de panier compacte -->
    <div class="cart-items-compact" id="cartItemsCompact">
        <div class="empty-cart-message">
            <i class="fas fa-shopping-cart"></i>
            <p>Panier vide</p>
            <small>Cliquez sur un produit pour l'ajouter</small>
        </div>
    </div>

    <!-- Note pour la cuisine -->
    <div class="kitchen-note-section mb-3">
        <label for="kitchenNote" class="form-label">
            <i class="fas fa-sticky-note"></i> Note pour la cuisine
        </label>
        <textarea class="form-control" id="kitchenNote" rows="2"
                  placeholder="Instructions spéciales pour la cuisine..."></textarea>
    </div>

    <!-- Boutons d'action compacts -->
    <div class="cart-actions-compact">
        <div class="main-actions">
            <button class="btn btn-success btn-lg flex-grow-1" onclick="POS.processPayment()" id="paymentBtn" disabled>
                <i class="fas fa-cash-register"></i> Paiement
            </button>
            <button class="btn btn-warning btn-sm kitchen-btn" onclick="POS.sendToKitchen()" id="kitchenBtn" disabled title="Envoyer à la cuisine">
                <i class="fas fa-utensils"></i>
            </button>
        </div>
        <div class="secondary-actions">
            <button class="btn btn-secondary btn-sm" onclick="POS.holdOrder()" id="holdBtn" disabled>
                <i class="fas fa-pause"></i> Pause
            </button>
            <button class="btn btn-danger btn-sm" onclick="POS.newOrder()">
                <i class="fas fa-plus"></i> Nouvelle
            </button>
        </div>
    </div>
</div>

<!-- Modal pour afficher le détail du panier -->
<div class="modal fade" id="cartDetailModal" tabindex="-1" aria-labelledby="cartDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="cartDetailModalLabel">
                    <i class="fas fa-shopping-cart me-2"></i>Détail du Panier
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="cartDetailItems">
                    <!-- Les articles détaillés du panier seront affichés ici -->
                </div>
                <div class="cart-detail-summary">
                    <div class="row">
                        <div class="col-6">
                            <strong>Nombre d'articles:</strong>
                        </div>
                        <div class="col-6 text-end">
                            <span id="modalCartItemsCount">0</span>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <h5><strong>Total:</strong></h5>
                        </div>
                        <div class="col-6 text-end">
                            <h5 class="text-success"><strong><span id="modalCartTotal">0.00 €</span></strong></h5>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Fermer
                </button>
                <button type="button" class="btn btn-warning" onclick="POS.sendToKitchen(); $('#cartDetailModal').modal('hide');">
                    <i class="fas fa-utensils me-2"></i>Envoyer à la cuisine
                </button>
                <button type="button" class="btn btn-success" onclick="POS.processPayment(); $('#cartDetailModal').modal('hide');">
                    <i class="fas fa-cash-register me-2"></i>Paiement
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour les commandes en attente -->
<div class="modal fade" id="heldOrdersModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Commandes en attente</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="heldOrdersList">
                    <!-- Les commandes en attente seront listées ici -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de paiement -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Paiement</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="paymentTotal" class="form-label">Total</label>
                    <h4><span id="paymentTotal">0.00 €</span></h4>
                </div>
                <div class="mb-3">
                    <label class="form-label d-block">Mode de paiement</label>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-lg btn-success payment-method" data-method="cash">
                            <i class="fas fa-money-bill-wave"></i> Espèces
                        </button>
                        <button type="button" class="btn btn-lg btn-primary payment-method" data-method="card">
                            <i class="fas fa-credit-card"></i> Carte bancaire
                        </button>
                        <button type="button" class="btn btn-lg btn-info payment-method" data-method="check">
                            <i class="fas fa-money-check"></i> Chèque
                        </button>
                        <button type="button" class="btn btn-lg btn-secondary payment-method" data-method="other">
                            <i class="fas fa-ellipsis-h"></i> Autre
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.cart-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Styles pour la sélection de table */
.table-selection {
    background-color: white;
    border-radius: var(--radius);
    padding: 15px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.table-selection .form-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.table-selection .form-select {
    border: 2px solid var(--border-color);
    border-radius: var(--radius);
    padding: 10px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.table-selection .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.table-selection .form-select option:disabled {
    color: #6c757d;
    background-color: #f8f9fa;
}

.selected-table-info {
    min-height: 20px;
}

#selectedTableInfo {
    font-style: italic;
    color: var(--success-color) !important;
    font-weight: 500;
}

/* Styles pour la section note de cuisine */
.kitchen-note-section {
    background-color: #fff8e1;
    border: 1px solid #ffcc02;
    border-radius: var(--radius);
    padding: 15px;
}

.kitchen-note-section .form-label {
    font-weight: 600;
    color: #f57c00;
    margin-bottom: 8px;
}

.kitchen-note-section .form-control {
    border: 2px solid #ffcc02;
    border-radius: var(--radius);
    font-size: 14px;
    resize: vertical;
}

.kitchen-note-section .form-control:focus {
    border-color: #f57c00;
    box-shadow: 0 0 0 0.2rem rgba(245, 124, 0, 0.25);
}

/* Nouveau design du panier */
.cart-header {
    background-color: white;
    border-radius: var(--radius);
    margin-bottom: 10px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.cart-summary {
    padding: 15px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-summary:hover {
    background-color: var(--light-color);
    border-radius: var(--radius);
}

.cart-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.cart-items-count {
    font-size: 14px;
    color: var(--secondary-color);
    font-weight: 500;
}

.cart-total-preview {
    font-size: 18px;
    font-weight: bold;
    color: var(--success-color);
}

.cart-items-compact {
    flex-grow: 1;
    background-color: white;
    border-radius: var(--radius);
    margin-bottom: 10px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.empty-cart-message {
    text-align: center;
    color: var(--secondary-color);
    padding: 20px;
}

.empty-cart-message i {
    font-size: 2rem;
    margin-bottom: 10px;
    opacity: 0.5;
}

.empty-cart-message p {
    margin-bottom: 5px;
    font-weight: 500;
}

.empty-cart-message small {
    opacity: 0.7;
}

.cart-actions-compact {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.main-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.kitchen-btn {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
}

.secondary-actions {
    display: flex;
    gap: 10px;
}

.secondary-actions .btn {
    flex: 1;
}

/* Styles pour la modal du panier */
.cart-detail-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

.cart-detail-item:hover {
    background-color: var(--light-color);
}

.cart-detail-item:last-child {
    border-bottom: none;
}

.cart-item-image {
    width: 50px;
    height: 50px;
    border-radius: var(--radius);
    object-fit: cover;
    margin-right: 15px;
    border: 1px solid var(--border-color);
}

.cart-item-info {
    flex-grow: 1;
}

.cart-item-name {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 5px;
    color: var(--dark);
}

.cart-item-price {
    color: var(--secondary-color);
    font-size: 14px;
    margin-bottom: 3px;
}

.cart-item-subtotal {
    color: var(--success-color);
    font-weight: 600;
    font-size: 15px;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: var(--light-color);
    border-radius: 20px;
    padding: 5px;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: none;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quantity-btn:hover {
    background-color: var(--primary-color);
    transform: scale(1.1);
}

.quantity-btn:disabled {
    background-color: var(--secondary-color);
    cursor: not-allowed;
    transform: none;
}

.quantity-display {
    min-width: 30px;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
}

.remove-item-btn {
    background-color: var(--danger-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.remove-item-btn:hover {
    background-color: #c82333;
    transform: scale(1.1);
}

.cart-detail-summary {
    background-color: var(--light-color);
    padding: 20px;
    border-radius: var(--radius);
    margin-top: 20px;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes stockUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); color: var(--danger-color); }
    100% { transform: scale(1); }
}

.stock-update {
    animation: stockUpdate 0.5s ease-in-out;
}

/* Aperçu compact du panier */
.cart-preview {
    padding: 15px;
    width: 100%;
}

.cart-preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    font-size: 14px;
}

.cart-preview-item:last-child {
    border-bottom: none;
}

.cart-preview-item .item-name {
    font-weight: 500;
    color: var(--dark);
    flex-grow: 1;
    margin-right: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.cart-preview-item .item-quantity {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 13px;
}

.more-items {
    text-align: center;
    color: var(--secondary-color);
    font-style: italic;
    font-size: 12px;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #eee;
}

/* Notifications d'ajout au panier */
.cart-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: var(--success-color);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 9999;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 14px;
}

.cart-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.cart-notification i {
    font-size: 16px;
}

/* Responsive */
@media (max-width: 1200px) {
    .cart-container {
        height: 400px;
    }

    .cart-notification {
        right: 10px;
        top: 10px;
        font-size: 13px;
        padding: 10px 16px;
    }
}

@media (max-width: 768px) {
    .kitchen-btn {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }

    .cart-summary {
        padding: 12px;
    }

    .cart-items-count {
        font-size: 13px;
    }

    .cart-total-preview {
        font-size: 16px;
    }
}
</style>