{% extends "base.html" %}

{% block title %}Commandes Cuisine{% endblock %}

{% block content %}

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-utensils"></i> Commandes en cours
        </h2>
        <div>
            <a href="{{ url_for('pos.ready_orders') }}" class="btn btn-success me-2">
                <i class="fas fa-bell"></i> Commandes prêtes
            </a>
            <a href="{{ url_for('pos.index') }}" class="btn btn-primary">
                <i class="fas fa-cash-register"></i> POS
            </a>
        </div>
    </div>

    <div class="row">
        {% for order in orders %}
        <div class="col-md-4 mb-4">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-receipt"></i> Commande #{{ order.reference }}
                        <small class="float-end">
                            {{ order.created_at.strftime('%H:%M') }}
                        </small>
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Informations de table et service -->
                    <div class="kitchen-info">
                        <div class="row">
                            {% if order.table %}
                            <div class="col-md-6">
                                <strong><i class="fas fa-table text-primary"></i> Table {{ order.table.number }}</strong>
                                {% if order.table.room %}
                                <br><small class="text-muted">{{ order.table.room.name }}</small>
                                {% endif %}
                            </div>
                            {% endif %}
                            <div class="col-md-6">
                                <strong><i class="fas fa-users text-success"></i> {{ order.covers_count }} couverts</strong>
                                <br><small class="text-muted">{{ order.service_type_display }}</small>
                            </div>
                        </div>
                    </div>

                    <!-- Articles de la commande -->
                    <div class="mb-3">
                        <h6 class="text-muted mb-2"><i class="fas fa-list"></i> Articles :</h6>
                        <ul class="list-group list-group-flush">
                            {% for item in order.items %}
                            <li class="list-group-item d-flex justify-content-between align-items-center px-0 py-1">
                                <span>{{ item.product.name }}</span>
                                <span class="badge bg-primary rounded-pill">{{ item.quantity }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>

                    <!-- Note de cuisine -->
                    {% if order.kitchen_note %}
                    <div class="kitchen-note">
                        <h6 class="text-dark mb-1">
                            <i class="fas fa-sticky-note text-warning"></i> Note cuisine :
                        </h6>
                        <p class="mb-0 text-dark">{{ order.kitchen_note }}</p>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <button class="btn btn-success w-100" onclick="markAsReady({{ order.id }})">
                        <i class="fas fa-check"></i> Marquer comme prêt
                    </button>
                </div>
            </div>
        </div>
        {% else %}
        <div class="col">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> Aucune commande en attente.
            </div>
        </div>
        {% endfor %}
    </div>
</div>

{% block extra_css %}
<style>
/* Désactiver toutes les animations et transitions pour la cuisine */
.card, .card-body, .card-header, .card-footer,
.bg-light, .border, .rounded, .list-group-item,
.badge, .text-muted, .text-primary, .text-success,
.bg-warning, .border-warning {
    transition: none !important;
    animation: none !important;
}

/* S'assurer que les éléments restent visibles */
.card-body > div {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* Style stable pour les informations importantes */
.kitchen-info {
    background-color: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
    padding: 0.75rem !important;
    margin-bottom: 1rem !important;
    border-radius: 0.375rem !important;
}

.kitchen-note {
    background-color: #fff3cd !important;
    border: 1px solid #ffc107 !important;
    padding: 0.75rem !important;
    border-radius: 0.375rem !important;
}
</style>
{% endblock %}

{% block scripts %}
<script>
function markAsReady(orderId) {
    if (confirm('Marquer cette commande comme prête ?')) {
        fetch(`/pos/kitchen/order/${orderId}/ready`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Recharger la page pour mettre à jour la liste
                location.reload();
            } else {
                alert('Erreur: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Une erreur est survenue');
        });
    }
}

// Rafraîchir la page toutes les 30 secondes
setInterval(() => {
    location.reload();
}, 30000);
</script>
{% endblock %}
{% endblock %}